# Generated by Django 5.1 on 2025-07-20 06:50

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0024_owner_business_license_shop_api_endpoint_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ComparisonCriteria',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='Criteria Name')),
                ('criteria_type', models.CharField(choices=[('price', 'Price'), ('rating', 'Rating'), ('delivery', 'Delivery Time'), ('reliability', 'Store Reliability'), ('customer_service', 'Customer Service'), ('return_policy', 'Return Policy'), ('availability', 'Availability'), ('brand_reputation', 'Brand Reputation')], max_length=20, verbose_name='Criteria Type')),
                ('weight', models.DecimalField(decimal_places=2, default=Decimal('1.0'), help_text='Importance weight for this criteria (0.1 to 5.0)', max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('0.1')), django.core.validators.MaxValueValidator(Decimal('5.0'))], verbose_name='Weight')),
                ('is_higher_better', models.BooleanField(default=True, help_text='Whether higher values are better for this criteria', verbose_name='Is Higher Better')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name_plural': 'Comparison Criteria',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ComparisonTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Template Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('category', models.CharField(choices=[('electronics', 'Electronics'), ('fashion', 'Fashion'), ('home', 'Home & Garden'), ('sports', 'Sports & Outdoors'), ('books', 'Books'), ('general', 'General')], default='general', max_length=20, verbose_name='Category')),
                ('is_default', models.BooleanField(default=False, verbose_name='Is Default Template')),
                ('usage_count', models.PositiveIntegerField(default=0, verbose_name='Usage Count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
            options={
                'ordering': ['-usage_count', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProductComparison',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(default='Product Comparison', max_length=200, verbose_name='Comparison Name')),
                ('is_public', models.BooleanField(default=False, help_text='Whether this comparison can be viewed by others', verbose_name='Is Public')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('products', models.ManyToManyField(help_text='Products being compared', related_name='comparisons', to='core.product')),
                ('user', models.ForeignKey(blank=True, help_text='User who created this comparison (null for anonymous)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='product_comparisons', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ComparisonShare',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('share_token', models.CharField(max_length=32, unique=True, verbose_name='Share Token')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='Expires At')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='View Count')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('comparison', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shares', to='comparison.productcomparison')),
            ],
        ),
        migrations.CreateModel(
            name='ComparisonResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('overall_scores', models.JSONField(default=dict, help_text='Calculated scores for each product', verbose_name='Overall Scores')),
                ('criteria_breakdown', models.JSONField(default=dict, help_text='Detailed breakdown by criteria', verbose_name='Criteria Breakdown')),
                ('best_deals', models.JSONField(default=list, help_text='Top deals identified in the comparison', verbose_name='Best Deals')),
                ('calculated_at', models.DateTimeField(auto_now=True, verbose_name='Calculated At')),
                ('winner_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='won_comparisons', to='core.product', verbose_name='Winner Product')),
                ('comparison', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='result', to='comparison.productcomparison')),
            ],
        ),
        migrations.CreateModel(
            name='TemplateCriteria',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_weight', models.DecimalField(blank=True, decimal_places=2, help_text='Override the default criteria weight for this template', max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.1')), django.core.validators.MaxValueValidator(Decimal('5.0'))], verbose_name='Custom Weight')),
                ('criteria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='comparison.comparisoncriteria')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='comparison.comparisontemplate')),
            ],
            options={
                'unique_together': {('template', 'criteria')},
            },
        ),
        migrations.AddField(
            model_name='comparisontemplate',
            name='criteria',
            field=models.ManyToManyField(related_name='templates', through='comparison.TemplateCriteria', to='comparison.comparisoncriteria'),
        ),
    ]
