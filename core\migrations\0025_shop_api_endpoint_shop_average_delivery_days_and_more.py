# Generated by Django 5.1 on 2025-07-20 07:18

import django.core.validators
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0024_owner_business_license_shop_api_endpoint_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='shop',
            name='api_endpoint',
            field=models.URLField(blank=True, help_text='Base API URL for external integration', null=True, verbose_name='API Endpoint'),
        ),
        migrations.AddField(
            model_name='shop',
            name='average_delivery_days',
            field=models.PositiveIntegerField(default=3, verbose_name='Average Delivery Days'),
        ),
        migrations.AddField(
            model_name='shop',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=12, verbose_name='Created At'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='shop',
            name='customer_service_rating',
            field=models.DecimalField(decimal_places=2, default=Decimal('5.0'), max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('5'))], verbose_name='Customer Service Rating'),
        ),
        migrations.AddField(
            model_name='shop',
            name='external_shop_id',
            field=models.CharField(blank=True, help_text='ID from external platform', max_length=200, null=True, verbose_name='External Shop ID'),
        ),
        migrations.AddField(
            model_name='shop',
            name='integration_status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('error', 'Error'), ('pending', 'Pending Setup')], default='active', max_length=20, verbose_name='Integration Status'),
        ),
        migrations.AddField(
            model_name='shop',
            name='last_sync_at',
            field=models.DateTimeField(blank=True, help_text='Last time data was synchronized from external source', null=True, verbose_name='Last Sync At'),
        ),
        migrations.AddField(
            model_name='shop',
            name='reliability_score',
            field=models.DecimalField(decimal_places=2, default=Decimal('5.0'), max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('0')), django.core.validators.MaxValueValidator(Decimal('5'))], verbose_name='Reliability Score'),
        ),
        migrations.AddField(
            model_name='shop',
            name='return_policy_days',
            field=models.PositiveIntegerField(default=30, verbose_name='Return Policy Days'),
        ),
        migrations.AddField(
            model_name='shop',
            name='shop_type',
            field=models.CharField(choices=[('internal', 'Internal Shop'), ('external', 'External Store'), ('marketplace', 'Marketplace'), ('api_integrated', 'API Integrated')], default='internal', help_text='Type of shop for integration purposes', max_length=20, verbose_name='Shop Type'),
        ),
    ]
