# Generated by Django 5.1 on 2025-07-20 06:50

import django.core.validators
import django.utils.timezone
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0023_remove_product_in_stock'),
    ]

    operations = [
        migrations.AddField(
            model_name='owner',
            name='business_license',
            field=models.CharField(blank=True, help_text='رقم الرخصة التجارية لصاحب المتجر', max_length=100, null=True, verbose_name='Business License'),
        ),
        # تم إزالة الحقول الأخرى مؤقتاً لتجنب مشاكل datetime
        # يمكن إضافتها في migration منفصل لاحقاً
    ]

