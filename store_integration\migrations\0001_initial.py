# Generated by Django 5.1 on 2025-07-20 06:50

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0024_owner_business_license_shop_api_endpoint_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StoreIntegrationConfig',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('platform', models.CharField(choices=[('shopify', 'Shopify'), ('woocommerce', 'WooCommerce'), ('magento', 'Magento'), ('amazon', 'Amazon'), ('ebay', 'eBay'), ('etsy', 'Etsy'), ('custom_api', 'Custom API'), ('csv_import', 'CSV Import')], max_length=20, verbose_name='Platform')),
                ('api_key', models.CharField(blank=True, max_length=500, null=True, verbose_name='API Key')),
                ('api_secret', models.CharField(blank=True, max_length=500, null=True, verbose_name='API Secret')),
                ('access_token', models.TextField(blank=True, null=True, verbose_name='Access Token')),
                ('store_url', models.URLField(blank=True, null=True, verbose_name='Store URL')),
                ('webhook_url', models.URLField(blank=True, null=True, verbose_name='Webhook URL')),
                ('sync_frequency', models.CharField(choices=[('realtime', 'Real-time'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly'), ('manual', 'Manual')], default='daily', max_length=20, verbose_name='Sync Frequency')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('last_sync_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Sync At')),
                ('sync_errors', models.TextField(blank=True, null=True, verbose_name='Sync Errors')),
                ('configuration', models.JSONField(blank=True, default=dict, verbose_name='Additional Configuration')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('shop', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='integration_config', to='core.shop')),
            ],
        ),
        migrations.CreateModel(
            name='SyncLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sync_type', models.CharField(choices=[('full', 'Full Sync'), ('incremental', 'Incremental Sync'), ('product', 'Product Sync'), ('price', 'Price Sync'), ('inventory', 'Inventory Sync')], max_length=20, verbose_name='Sync Type')),
                ('status', models.CharField(choices=[('started', 'Started'), ('completed', 'Completed'), ('failed', 'Failed'), ('partial', 'Partial Success')], max_length=20, verbose_name='Status')),
                ('started_at', models.DateTimeField(auto_now_add=True, verbose_name='Started At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('products_processed', models.PositiveIntegerField(default=0, verbose_name='Products Processed')),
                ('products_updated', models.PositiveIntegerField(default=0, verbose_name='Products Updated')),
                ('products_created', models.PositiveIntegerField(default=0, verbose_name='Products Created')),
                ('errors_count', models.PositiveIntegerField(default=0, verbose_name='Errors Count')),
                ('error_details', models.TextField(blank=True, null=True, verbose_name='Error Details')),
                ('summary', models.JSONField(blank=True, default=dict, verbose_name='Sync Summary')),
                ('integration_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_logs', to='store_integration.storeintegrationconfig')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='PriceHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Price')),
                ('original_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Original Price')),
                ('currency', models.CharField(default='USD', max_length=3, verbose_name='Currency')),
                ('is_available', models.BooleanField(default=True, verbose_name='Is Available')),
                ('stock_quantity', models.PositiveIntegerField(blank=True, null=True, verbose_name='Stock Quantity')),
                ('recorded_at', models.DateTimeField(auto_now_add=True, verbose_name='Recorded At')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_history', to='core.product')),
                ('shop', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_history', to='core.shop')),
            ],
            options={
                'ordering': ['-recorded_at'],
                'indexes': [models.Index(fields=['product', 'shop', '-recorded_at'], name='store_integ_product_c2f81b_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProductMapping',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('external_product_id', models.CharField(max_length=200, verbose_name='External Product ID')),
                ('external_sku', models.CharField(blank=True, max_length=200, null=True, verbose_name='External SKU')),
                ('external_url', models.URLField(blank=True, null=True, verbose_name='External Product URL')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('last_sync_at', models.DateTimeField(blank=True, null=True, verbose_name='Last Sync At')),
                ('sync_status', models.CharField(choices=[('synced', 'Synced'), ('pending', 'Pending'), ('error', 'Error'), ('disabled', 'Disabled')], default='pending', max_length=20, verbose_name='Sync Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('local_product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='external_mappings', to='core.product')),
                ('integration_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_mappings', to='store_integration.storeintegrationconfig')),
            ],
            options={
                'unique_together': {('integration_config', 'external_product_id')},
            },
        ),
    ]
