<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Best in Click - أفضل في النقر{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2563eb;
            --secondary-blue: #1e40af;
            --accent-yellow: #fbbf24;
            --light-yellow: #fef3c7;
            --pure-white: #ffffff;
            --light-gray: #f8fafc;
            --text-dark: #1e293b;
            --text-light: #64748b;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--light-gray) 0%, var(--pure-white) 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header Styles - 30% Blue */
        .main-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--pure-white) !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand:hover {
            color: var(--light-yellow) !important;
            transform: translateY(-1px);
            transition: all 0.3s ease;
        }

        /* Navigation Styles */
        .navbar-nav .nav-link {
            color: var(--pure-white) !important;
            font-weight: 500;
            padding: 0.75rem 1.25rem !important;
            border-radius: 8px;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--accent-yellow) !important;
            transform: translateY(-2px);
        }

        .navbar-toggler {
            border: 2px solid var(--pure-white);
            border-radius: 8px;
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* Main Content Area - 60% White */
        .main-content {
            background: var(--pure-white);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            padding: 2rem;
            margin: 2rem 0;
            min-height: 60vh;
        }

        /* Accent Elements - 10% Yellow */
        .accent-element {
            background: linear-gradient(135deg, var(--accent-yellow) 0%, #f59e0b 100%);
            color: var(--text-dark);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 4px 16px rgba(251, 191, 36, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.4);
        }

        .btn-accent {
            background: linear-gradient(135deg, var(--accent-yellow) 0%, #f59e0b 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: var(--text-dark);
            box-shadow: 0 4px 16px rgba(251, 191, 36, 0.3);
            transition: all 0.3s ease;
        }

        .btn-accent:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(251, 191, 36, 0.4);
            color: var(--text-dark);
        }

        /* Footer Styles */
        .main-footer {
            background: linear-gradient(135deg, var(--text-dark) 0%, #0f172a 100%);
            color: var(--pure-white);
            padding: 3rem 0 2rem;
            margin-top: 4rem;
        }

        .footer-content {
            text-align: center;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--pure-white);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--accent-yellow);
            transform: translateY(-1px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                margin: 1rem 0;
                padding: 1.5rem;
                border-radius: 12px;
            }

            .navbar-brand {
                font-size: 1.5rem;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body class="fade-in">
    <!-- Header Section - 30% Blue -->
    <header class="main-header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand slide-in" href="/">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Best in Click
                    <small class="d-block" style="font-size: 0.6em; opacity: 0.9;">أفضل في النقر</small>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                        aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/categories/">
                                <i class="fas fa-th-large me-1"></i>
                                الفئات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/brands/">
                                <i class="fas fa-tags me-1"></i>
                                العلامات التجارية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link accent-element d-inline-block" href="/recommendations/">
                                <i class="fas fa-robot me-1"></i>
                                توصيات الذكي
                            </a>
                        </li>
                        {% if user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link" href="/dashboard/">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    لوحة التحكم
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/compare/">
                                    <i class="fas fa-balance-scale me-1"></i>
                                    مقارنة
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="/logout/">
                                    <i class="fas fa-sign-out-alt me-1"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link btn-accent d-inline-block" href="/login/">
                                    <i class="fas fa-sign-in-alt me-1"></i>
                                    تسجيل الدخول
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content Area - 60% White -->
    <main class="container">
        <div class="main-content fade-in">
            {% block content %}
            <div class="text-center py-5">
                <h1 class="display-4 mb-4" style="color: var(--primary-blue);">
                    مرحباً بك في Best in Click
                </h1>
                <p class="lead text-muted">
                    منصة التجارة الإلكترونية الذكية لأفضل تجربة تسوق
                </p>
                <div class="accent-element d-inline-block mt-3">
                    <i class="fas fa-star me-2"></i>
                    تجربة تسوق مميزة مع الذكاء الاصطناعي
                </div>
            </div>
            {% endblock %}
        </div>
    </main>

    <!-- Footer Section -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="/about/"><i class="fas fa-info-circle me-1"></i>حول الموقع</a>
                    <a href="/contact/"><i class="fas fa-envelope me-1"></i>اتصل بنا</a>
                    <a href="/privacy/"><i class="fas fa-shield-alt me-1"></i>سياسة الخصوصية</a>
                    <a href="/terms/"><i class="fas fa-file-contract me-1"></i>الشروط والأحكام</a>
                </div>
                <div class="social-links mb-3">
                    <a href="#" class="me-3" style="color: var(--accent-yellow);">
                        <i class="fab fa-facebook-f fa-lg"></i>
                    </a>
                    <a href="#" class="me-3" style="color: var(--accent-yellow);">
                        <i class="fab fa-twitter fa-lg"></i>
                    </a>
                    <a href="#" class="me-3" style="color: var(--accent-yellow);">
                        <i class="fab fa-instagram fa-lg"></i>
                    </a>
                    <a href="#" style="color: var(--accent-yellow);">
                        <i class="fab fa-linkedin-in fa-lg"></i>
                    </a>
                </div>
                <p class="mb-0">
                    <i class="fas fa-copyright me-1"></i>
                    2024 Best in Click. جميع الحقوق محفوظة.
                </p>
                <small class="text-muted">
                    منصة التجارة الإلكترونية الذكية | تطوير احترافي
                </small>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add smooth scrolling and interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to cards and buttons
            const interactiveElements = document.querySelectorAll('.btn, .card, .nav-link');
            interactiveElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add loading animation
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.opacity = '0';
                mainContent.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    mainContent.style.transition = 'all 0.6s ease';
                    mainContent.style.opacity = '1';
                    mainContent.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    </script>
    {% block extra_scripts %}{% endblock %}
</body>
</html>
