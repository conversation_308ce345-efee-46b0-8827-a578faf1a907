# Generated by Django 5.1 on 2025-07-20 06:50

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0024_owner_business_license_shop_api_endpoint_and_more'),
        ('reviews', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EngagementEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_id', models.CharField(max_length=100, verbose_name='Session ID')),
                ('event_type', models.CharField(choices=[('page_view', 'Page View'), ('product_view', 'Product View'), ('search', 'Search'), ('filter_applied', 'Filter Applied'), ('sort_applied', 'Sort Applied'), ('product_like', 'Product Like'), ('product_dislike', 'Product Dislike'), ('add_to_cart', 'Add to Cart'), ('remove_from_cart', 'Remove from Cart'), ('checkout_started', 'Checkout Started'), ('purchase_completed', 'Purchase Completed'), ('review_submitted', 'Review Submitted'), ('comparison_created', 'Comparison Created'), ('recommendation_clicked', 'Recommendation Clicked'), ('share_product', 'Share Product'), ('save_product', 'Save Product')], max_length=30, verbose_name='Event Type')),
                ('event_data', models.JSONField(blank=True, default=dict, verbose_name='Event Data')),
                ('page_url', models.URLField(blank=True)),
                ('referrer_url', models.URLField(blank=True)),
                ('user_agent', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='ProductEngagement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('unique_views', models.PositiveIntegerField(default=0)),
                ('average_view_duration', models.DurationField(blank=True, null=True)),
                ('total_likes', models.PositiveIntegerField(default=0)),
                ('total_dislikes', models.PositiveIntegerField(default=0)),
                ('total_shares', models.PositiveIntegerField(default=0)),
                ('total_saves', models.PositiveIntegerField(default=0)),
                ('add_to_cart_count', models.PositiveIntegerField(default=0)),
                ('purchase_count', models.PositiveIntegerField(default=0)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=5, verbose_name='Conversion Rate (%)')),
                ('total_reviews', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=3, verbose_name='Average Rating')),
                ('review_sentiment_score', models.DecimalField(decimal_places=2, default=Decimal('0.0'), max_digits=3, verbose_name='Average Sentiment Score')),
                ('comparison_count', models.PositiveIntegerField(default=0, verbose_name='Times Compared')),
                ('won_comparisons', models.PositiveIntegerField(default=0, verbose_name='Won Comparisons')),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ReviewHelpfulness',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('vote', models.CharField(choices=[('helpful', 'Helpful'), ('not_helpful', 'Not Helpful')], max_length=20, verbose_name='Vote')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='StoreReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('overall_rating', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Overall Rating')),
                ('delivery_rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], verbose_name='Delivery Rating')),
                ('customer_service_rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], verbose_name='Customer Service Rating')),
                ('product_quality_rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], verbose_name='Product Quality Rating')),
                ('value_for_money_rating', models.PositiveIntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], verbose_name='Value for Money Rating')),
                ('title', models.CharField(blank=True, max_length=200, verbose_name='Review Title')),
                ('comment', models.TextField(blank=True, verbose_name='Review Comment')),
                ('order_reference', models.CharField(blank=True, max_length=100, verbose_name='Order Reference')),
                ('purchase_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Purchase Amount')),
                ('sentiment_score', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(Decimal('-1')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='Sentiment Score')),
                ('helpfulness_score', models.PositiveIntegerField(default=0)),
                ('likes', models.PositiveIntegerField(default=0)),
                ('dislikes', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserFeedback',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(blank=True, help_text='For anonymous feedback', max_length=254, verbose_name='Contact Email')),
                ('feedback_type', models.CharField(choices=[('bug_report', 'Bug Report'), ('feature_request', 'Feature Request'), ('general_feedback', 'General Feedback'), ('complaint', 'Complaint'), ('compliment', 'Compliment'), ('suggestion', 'Suggestion')], max_length=20, verbose_name='Feedback Type')),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10, verbose_name='Priority')),
                ('status', models.CharField(choices=[('new', 'New'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='new', max_length=20, verbose_name='Status')),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('page_url', models.URLField(blank=True, help_text='URL where the feedback was submitted', verbose_name='Page URL')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('sentiment_score', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(Decimal('-1')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='Sentiment Score')),
                ('category_prediction', models.CharField(blank=True, max_length=50, verbose_name='AI Category Prediction')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='review',
            name='cons',
            field=models.TextField(blank=True, help_text="What didn't you like about this product?", verbose_name='Cons'),
        ),
        migrations.AddField(
            model_name='review',
            name='dislikes',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='review',
            name='helpfulness_score',
            field=models.PositiveIntegerField(default=0, help_text='Number of users who found this review helpful', verbose_name='Helpfulness Score'),
        ),
        migrations.AddField(
            model_name='review',
            name='likes',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='review',
            name='pros',
            field=models.TextField(blank=True, help_text='What did you like about this product?', verbose_name='Pros'),
        ),
        migrations.AddField(
            model_name='review',
            name='purchase_date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Purchase Date'),
        ),
        migrations.AddField(
            model_name='review',
            name='reports',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='review',
            name='sentiment_label',
            field=models.CharField(blank=True, choices=[('positive', 'Positive'), ('neutral', 'Neutral'), ('negative', 'Negative')], max_length=20, null=True, verbose_name='Sentiment Label'),
        ),
        migrations.AddField(
            model_name='review',
            name='sentiment_score',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='AI-calculated sentiment score (-1 to 1)', max_digits=3, null=True, validators=[django.core.validators.MinValueValidator(Decimal('-1')), django.core.validators.MaxValueValidator(Decimal('1'))], verbose_name='Sentiment Score'),
        ),
        migrations.AddField(
            model_name='review',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending Moderation'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('flagged', 'Flagged for Review')], default='pending', max_length=20, verbose_name='Review Status'),
        ),
        migrations.AddField(
            model_name='review',
            name='title',
            field=models.CharField(blank=True, max_length=200, verbose_name='Review Title'),
        ),
        migrations.AddField(
            model_name='review',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='review',
            name='verified_purchase',
            field=models.BooleanField(default=False, help_text='Whether this review is from a verified purchase', verbose_name='Verified Purchase'),
        ),
        migrations.AlterField(
            model_name='review',
            name='comment',
            field=models.TextField(blank=True, null=True, verbose_name='Review Comment'),
        ),
        migrations.AlterField(
            model_name='review',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='review',
            name='rating',
            field=models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Product Rating'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['product', '-created_at'], name='reviews_rev_product_d800fc_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['user', '-created_at'], name='reviews_rev_user_id_eeecea_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['status', '-created_at'], name='reviews_rev_status_70a1f9_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['sentiment_label', '-created_at'], name='reviews_rev_sentime_0654fa_idx'),
        ),
        migrations.AddField(
            model_name='engagementevent',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='engagement_events', to='core.product'),
        ),
        migrations.AddField(
            model_name='engagementevent',
            name='shop',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='engagement_events', to='core.shop'),
        ),
        migrations.AddField(
            model_name='engagementevent',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='engagement_events', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='productengagement',
            name='product',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='engagement_metrics', to='core.product'),
        ),
        migrations.AddField(
            model_name='reviewhelpfulness',
            name='review',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='helpfulness_votes', to='reviews.review'),
        ),
        migrations.AddField(
            model_name='reviewhelpfulness',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_helpfulness_votes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='storereview',
            name='shop',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='store_reviews', to='core.shop'),
        ),
        migrations.AddField(
            model_name='storereview',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='store_reviews', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userfeedback',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='platform_feedback', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='engagementevent',
            index=models.Index(fields=['user', '-timestamp'], name='reviews_eng_user_id_461aa8_idx'),
        ),
        migrations.AddIndex(
            model_name='engagementevent',
            index=models.Index(fields=['session_id', '-timestamp'], name='reviews_eng_session_9fd9c7_idx'),
        ),
        migrations.AddIndex(
            model_name='engagementevent',
            index=models.Index(fields=['event_type', '-timestamp'], name='reviews_eng_event_t_4f15d7_idx'),
        ),
        migrations.AddIndex(
            model_name='engagementevent',
            index=models.Index(fields=['product', '-timestamp'], name='reviews_eng_product_97e2dc_idx'),
        ),
        migrations.AddIndex(
            model_name='engagementevent',
            index=models.Index(fields=['shop', '-timestamp'], name='reviews_eng_shop_id_f39ce7_idx'),
        ),
        migrations.AddIndex(
            model_name='productengagement',
            index=models.Index(fields=['product', '-last_updated'], name='reviews_pro_product_476271_idx'),
        ),
        migrations.AddIndex(
            model_name='productengagement',
            index=models.Index(fields=['-total_views'], name='reviews_pro_total_v_4997bf_idx'),
        ),
        migrations.AddIndex(
            model_name='productengagement',
            index=models.Index(fields=['-conversion_rate'], name='reviews_pro_convers_1e9480_idx'),
        ),
        migrations.AddIndex(
            model_name='productengagement',
            index=models.Index(fields=['-average_rating'], name='reviews_pro_average_333ce5_idx'),
        ),
        migrations.AddIndex(
            model_name='reviewhelpfulness',
            index=models.Index(fields=['review', 'vote'], name='reviews_rev_review__7b5acc_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='reviewhelpfulness',
            unique_together={('review', 'user')},
        ),
        migrations.AddIndex(
            model_name='storereview',
            index=models.Index(fields=['shop', '-created_at'], name='reviews_sto_shop_id_9e8b5a_idx'),
        ),
        migrations.AddIndex(
            model_name='storereview',
            index=models.Index(fields=['user', '-created_at'], name='reviews_sto_user_id_b1bbb3_idx'),
        ),
        migrations.AddIndex(
            model_name='storereview',
            index=models.Index(fields=['overall_rating', '-created_at'], name='reviews_sto_overall_4bc244_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='storereview',
            unique_together={('shop', 'user')},
        ),
        migrations.AddIndex(
            model_name='userfeedback',
            index=models.Index(fields=['feedback_type', '-created_at'], name='reviews_use_feedbac_0f31c3_idx'),
        ),
        migrations.AddIndex(
            model_name='userfeedback',
            index=models.Index(fields=['status', '-created_at'], name='reviews_use_status_4741a0_idx'),
        ),
        migrations.AddIndex(
            model_name='userfeedback',
            index=models.Index(fields=['priority', '-created_at'], name='reviews_use_priorit_204948_idx'),
        ),
    ]
