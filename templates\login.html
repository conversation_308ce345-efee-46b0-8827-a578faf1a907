{% extends 'core/base.html' %}
{% block title %}Login | Best in Click{% endblock %}

{% block content %}
<style>
  .login-container {
    min-height: 90vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2rem;
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
  }

  .login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    position: relative;
    z-index: 1;
  }

  .form-control {
    border: 2px solid #e9ecef;
    border-radius: 1rem;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    transform: translateY(-2px);
  }

  .btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    border-radius: 1rem;
    padding: 1rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before {
    left: 100%;
  }

  .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(13, 110, 253, 0.4);
  }

  .social-login {
    border: 2px solid #e9ecef;
    border-radius: 1rem;
    padding: 0.75rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }

  .social-login:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-decoration: none;
  }

  .brand-icon {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 3.5rem;
    margin-bottom: 1rem;
  }

  .welcome-text {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
  }

  .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
  }

  .input-group-text {
    background: transparent;
    border: 2px solid #e9ecef;
    border-left: none;
    border-radius: 0 1rem 1rem 0;
  }

  .loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .success-message {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 1rem;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
  }
</style>

<div class="container-fluid login-container d-flex align-items-center justify-content-center">
  <div class="row w-100 justify-content-center">
    <div class="col-md-6 col-lg-5 col-xl-4">
      <div class="card login-card">
        <div class="card-body p-5">
          <div class="text-center mb-4">
            <div class="brand-icon">
              <i class="fas fa-shopping-bag"></i>
            </div>
            <h2 class="welcome-text mb-2">Welcome Back!</h2>
            <p class="text-muted">Sign in to your Best in Click account</p>
          </div>

          {% if success %}
            <div class="success-message">
              <i class="fas fa-check-circle me-2"></i>
              Login successful! Redirecting...
            </div>
          {% endif %}

          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}

          {% if form.errors %}
            <div class="alert alert-danger alert-dismissible fade show">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <strong>Login Failed:</strong>
              {% for field in form %}
                {% for error in field.errors %}
                  <div>{{ error }}</div>
                {% endfor %}
              {% endfor %}
              {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
              {% endfor %}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endif %}

          {% if not success %}
          <form method="post" action="" id="loginForm">
            {% csrf_token %}
            {{ form.hidden_fields }}

            <div class="mb-4">
              <label for="{{ form.username.id_for_label }}" class="form-label">
                <i class="fas fa-user me-2"></i>{{ form.username.label }}
              </label>
              {{ form.username }}
              {% if form.username.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.username.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="mb-4">
              <label for="{{ form.password.id_for_label }}" class="form-label">
                <i class="fas fa-lock me-2"></i>{{ form.password.label }}
              </label>
              <div class="input-group">
                {{ form.password }}
                <button class="input-group-text" type="button" id="togglePassword">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
              {% if form.password.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.password.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="mb-4 d-flex justify-content-between align-items-center">
              <div class="form-check">
                {{ form.remember_me }}
                <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                  {{ form.remember_me.label }}
                </label>
              </div>
              <a href="/forgot-password/" class="text-decoration-none text-primary">
                <i class="fas fa-key me-1"></i>Forgot password?
              </a>
            </div>

            <button type="submit" class="btn btn-primary w-100 mb-4" id="loginBtn">
              <span class="btn-text">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
              </span>
              <div class="loading-spinner"></div>
            </button>
          </form>

          <!-- Social Login Options -->
          <div class="text-center mb-3">
            <small class="text-muted">Or continue with</small>
          </div>

          <div class="row g-2 mb-4">
            <div class="col-6">
              <a href="#" class="btn social-login w-100 text-decoration-none">
                <i class="fab fa-google text-danger me-2"></i>Google
              </a>
            </div>
            <div class="col-6">
              <a href="#" class="btn social-login w-100 text-decoration-none">
                <i class="fab fa-facebook text-primary me-2"></i>Facebook
              </a>
            </div>
          </div>
          {% endif %}

          <div class="text-center">
            <span class="text-muted">Don't have an account?</span>
            <a href="/register/" class="text-decoration-none fw-semibold">Create Account</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const usernameInput = document.getElementById('username');

    // Toggle password visibility
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.setAttribute('title', 'Hide password');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.setAttribute('title', 'Show password');
            }
        });
    }

    // Enhanced form validation and submission
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            const username = usernameInput.value.trim();
            const password = passwordInput.value;

            // Clear previous error states
            clearErrorStates();

            let hasErrors = false;

            // Validate username/email
            if (!username) {
                showFieldError(usernameInput, 'Username or email is required');
                hasErrors = true;
            } else if (username.length < 3) {
                showFieldError(usernameInput, 'Username must be at least 3 characters');
                hasErrors = true;
            }

            // Validate password
            if (!password) {
                showFieldError(passwordInput, 'Password is required');
                hasErrors = true;
            } else if (password.length < 6) {
                showFieldError(passwordInput, 'Password must be at least 6 characters');
                hasErrors = true;
            }

            if (hasErrors) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            showLoadingState();
        });
    }

    // Input field enhancements
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim() === '') {
                this.classList.remove('is-valid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                this.classList.remove('is-invalid');
                const feedback = this.parentElement.querySelector('.invalid-feedback');
                if (feedback) feedback.style.display = 'none';
            }

            if (this.value.trim() !== '') {
                this.classList.add('is-valid');
            }
        });
    });

    // Helper functions
    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');

        let feedback = field.parentElement.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentElement.appendChild(feedback);
        }

        feedback.textContent = message;
        feedback.style.display = 'block';
    }

    function clearErrorStates() {
        const invalidFields = document.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => {
            field.classList.remove('is-invalid');
        });

        const feedbacks = document.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => {
            if (!feedback.classList.contains('d-block')) {
                feedback.style.display = 'none';
            }
        });
    }

    function showLoadingState() {
        if (loginBtn) {
            loginBtn.disabled = true;
            const btnText = loginBtn.querySelector('.btn-text');
            const spinner = loginBtn.querySelector('.loading-spinner');

            if (btnText) btnText.style.display = 'none';
            if (spinner) spinner.style.display = 'inline-block';

            loginBtn.innerHTML = '<div class="loading-spinner"></div> Signing in...';
        }
    }



    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && (e.target === usernameInput || e.target === passwordInput)) {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });
});
</script>

{% if success %}
<script>
// Auto-redirect after successful login
setTimeout(function() {
    window.location.href = '{{ request.GET.next|default:"/" }}';
}, 2000);
</script>
{% endif %}
{% endblock %}
