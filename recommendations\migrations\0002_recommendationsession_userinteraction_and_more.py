# Generated by Django 5.1 on 2025-07-20 06:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0024_owner_business_license_shop_api_endpoint_and_more'),
        ('recommendations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='RecommendationSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(help_text='Browser session ID for anonymous users', max_length=100, verbose_name='Session ID')),
                ('trigger_interaction', models.CharField(blank=True, choices=[('view', 'View'), ('like', 'Like'), ('dislike', 'Dislike'), ('add_to_cart', 'Add to Cart'), ('remove_from_cart', 'Remove from Cart'), ('purchase', 'Purchase'), ('review', 'Review'), ('share', 'Share'), ('compare', 'Compare'), ('wishlist_add', 'Add to Wishlist'), ('wishlist_remove', 'Remove from Wishlist'), ('search', 'Search'), ('click', 'Click')], max_length=20, null=True, verbose_name='Trigger Interaction')),
                ('recommended_products', models.JSONField(default=list, help_text='List of recommended product IDs', verbose_name='Recommended Products')),
                ('recommendation_types', models.JSONField(default=dict, help_text='Types of recommendations provided', verbose_name='Recommendation Types')),
                ('clicks', models.PositiveIntegerField(default=0, help_text='Number of recommended products clicked', verbose_name='Clicks')),
                ('conversions', models.PositiveIntegerField(default=0, help_text='Number of recommended products purchased', verbose_name='Conversions')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('trigger_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='triggered_sessions', to='core.product', verbose_name='Trigger Product')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='recommendation_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', '-created_at'], name='recommendat_user_id_78b8c2_idx'), models.Index(fields=['session_id', '-created_at'], name='recommendat_session_d84ada_idx'), models.Index(fields=['trigger_product', '-created_at'], name='recommendat_trigger_2035ea_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserInteraction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interaction_type', models.CharField(choices=[('view', 'View'), ('like', 'Like'), ('dislike', 'Dislike'), ('add_to_cart', 'Add to Cart'), ('remove_from_cart', 'Remove from Cart'), ('purchase', 'Purchase'), ('review', 'Review'), ('share', 'Share'), ('compare', 'Compare'), ('wishlist_add', 'Add to Wishlist'), ('wishlist_remove', 'Remove from Wishlist'), ('search', 'Search'), ('click', 'Click')], max_length=20, verbose_name='Interaction Type')),
                ('last_interaction_type', models.CharField(blank=True, choices=[('view', 'View'), ('like', 'Like'), ('dislike', 'Dislike'), ('add_to_cart', 'Add to Cart'), ('remove_from_cart', 'Remove from Cart'), ('purchase', 'Purchase'), ('review', 'Review'), ('share', 'Share'), ('compare', 'Compare'), ('wishlist_add', 'Add to Wishlist'), ('wishlist_remove', 'Remove from Wishlist'), ('search', 'Search'), ('click', 'Click')], max_length=20, null=True, verbose_name='Last Interaction Type')),
                ('interaction_count', models.PositiveIntegerField(default=1, verbose_name='Interaction Count')),
                ('context', models.JSONField(blank=True, default=dict, help_text='Additional context about the interaction', verbose_name='Interaction Context')),
                ('first_interaction_at', models.DateTimeField(auto_now_add=True, verbose_name='First Interaction At')),
                ('last_interaction_at', models.DateTimeField(auto_now=True, verbose_name='Last Interaction At')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_interactions', to='core.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_interactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_interaction_at'],
                'indexes': [models.Index(fields=['user', 'last_interaction_at'], name='recommendat_user_id_38b6d5_idx'), models.Index(fields=['product', 'interaction_type'], name='recommendat_product_49a5e1_idx'), models.Index(fields=['interaction_type', 'last_interaction_at'], name='recommendat_interac_e2b50a_idx')],
                'unique_together': {('user', 'product')},
            },
        ),
        migrations.CreateModel(
            name='UserProductWeight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('weight', models.FloatField(default=0.0, help_text='Calculated weight based on user interactions', verbose_name='Weight')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_weights', to='core.product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_weights', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-weight'],
                'indexes': [models.Index(fields=['user', '-weight'], name='recommendat_user_id_88d85e_idx'), models.Index(fields=['product', '-weight'], name='recommendat_product_de3900_idx')],
                'unique_together': {('user', 'product')},
            },
        ),
    ]
