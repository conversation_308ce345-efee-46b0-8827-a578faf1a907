{% extends 'core/base.html' %}
{% block title %}التسجيل | Best in Click{% endblock %}
{% block content %}
<style>
  .register-container {
    min-height: 90vh;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border-radius: 2rem;
    margin: 1rem 0;
    position: relative;
    overflow: hidden;
  }

  .register-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .register-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    position: relative;
    z-index: 1;
  }

  .nav-tabs {
    border: none;
    background: var(--light-gray);
    border-radius: 1.5rem;
    padding: 0.5rem;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.1);
  }

  .nav-tabs .nav-link {
    border: none;
    border-radius: 1.25rem;
    color: var(--text-light);
    font-weight: 600;
    transition: all 0.4s ease;
    padding: 1rem 1.5rem;
    position: relative;
    overflow: hidden;
  }

  .nav-tabs .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
  }

  .nav-tabs .nav-link:hover::before {
    left: 100%;
  }

  .nav-tabs .nav-link.active {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: var(--pure-white);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4);
    transform: translateY(-2px);
  }

  .form-control {
    border: 2px solid #e2e8f0;
    border-radius: 1rem;
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--pure-white);
  }

  .form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.15);
    transform: translateY(-1px);
  }

  .form-label {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  .btn-register {
    background: linear-gradient(135deg, var(--accent-yellow) 0%, #f59e0b 100%);
    border: none;
    border-radius: 1rem;
    padding: 1rem 2rem;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-dark);
    transition: all 0.4s ease;
    box-shadow: 0 8px 20px rgba(251, 191, 36, 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-register::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-register:hover::before {
    left: 100%;
  }

  .btn-register:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(251, 191, 36, 0.4);
    color: var(--text-dark);
  }

  .btn-register-store {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    border: none;
    border-radius: 1rem;
    padding: 1rem 2rem;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--pure-white);
    transition: all 0.4s ease;
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
    position: relative;
    overflow: hidden;
  }

  .btn-register-store::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
  }

  .btn-register-store:hover::before {
    left: 100%;
  }

  .btn-register-store:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(37, 99, 235, 0.4);
    color: var(--pure-white);
  }

  .password-strength {
    height: 6px;
    border-radius: 3px;
    margin-top: 0.75rem;
    transition: all 0.4s ease;
    background: #e2e8f0;
  }

  .role-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }

  .alert {
    border-radius: 1rem;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
  }

  .alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
  }

  .alert-success {
    background: linear-gradient(135deg, var(--light-yellow) 0%, #fef3c7 100%);
    color: var(--text-dark);
  }

  .form-check-input:checked {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
  }

  .input-group .btn {
    border-radius: 0 1rem 1rem 0;
    border: 2px solid #e2e8f0;
    border-left: none;
  }

  .page-title {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-yellow) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
  }
</style>

<div class="container-fluid register-container d-flex align-items-center justify-content-center">
  <div class="row w-100 justify-content-center">
    <div class="col-md-10 col-lg-8 col-xl-6">
      <div class="card register-card">
        <div class="card-body p-5">
          <div class="text-center mb-5">
            <div class="mb-4">
              <i class="fas fa-user-plus fa-4x page-title"></i>
            </div>
            <h1 class="fw-bold page-title mb-3">انضم إلى Best in Click</h1>
            <p class="text-muted fs-5">اختر دورك وأنشئ حسابك للبدء في رحلة التسوق الذكي</p>
            <div class="d-flex justify-content-center gap-3 mt-4">
              <div class="badge bg-primary fs-6 px-3 py-2">
                <i class="fas fa-shield-alt me-2"></i>آمن ومحمي
              </div>
              <div class="badge bg-warning text-dark fs-6 px-3 py-2">
                <i class="fas fa-rocket me-2"></i>سريع وسهل
              </div>
            </div>
          </div>

          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}

          {% if form.errors %}
            <div class="alert alert-danger alert-dismissible fade show">
              <i class="fas fa-exclamation-triangle me-2"></i>
              {% for field in form %}
                {% for error in field.errors %}
                  <div>{{ error }}</div>
                {% endfor %}
              {% endfor %}
              {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
              {% endfor %}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endif %}
          <ul class="nav nav-tabs mb-5" id="registerTabs" role="tablist">
            <li class="nav-item flex-fill" role="presentation">
              <button class="nav-link active w-100" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab" aria-controls="customer" aria-selected="true">
                <i class="fas fa-shopping-cart me-2"></i>
                <span class="fw-bold">عميل</span>
                <small class="d-block mt-1 opacity-75">للتسوق والشراء</small>
              </button>
            </li>
            <li class="nav-item flex-fill" role="presentation">
              <button class="nav-link w-100" id="storeowner-tab" data-bs-toggle="tab" data-bs-target="#storeowner" type="button" role="tab" aria-controls="storeowner" aria-selected="false">
                <i class="fas fa-store me-2"></i>
                <span class="fw-bold">صاحب متجر</span>
                <small class="d-block mt-1 opacity-75">للبيع والتجارة</small>
              </button>
            </li>
          </ul>
          <div class="tab-content" id="registerTabsContent">
            <div class="tab-pane fade show active" id="customer" role="tabpanel" aria-labelledby="customer-tab">
              <div class="text-center mb-5">
                <div class="role-icon" style="color: var(--accent-yellow);">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="fw-bold mb-3" style="color: var(--primary-blue);">انضم كعميل</h3>
                <p class="text-muted">اكتشف منتجات رائعة واستمتع بتجربة تسوق شخصية مميزة</p>
                <div class="row g-2 justify-content-center mt-3">
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-star text-warning me-1"></i>توصيات ذكية
                    </span>
                  </div>
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-heart text-danger me-1"></i>قائمة المفضلة
                    </span>
                  </div>
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-gift text-success me-1"></i>عروض حصرية
                    </span>
                  </div>
                </div>
              </div>

              <form method="post" id="customerForm">
                {% csrf_token %}
                <input type="hidden" name="role" value="customer">

                <div class="row">
                  <div class="col-md-6 mb-4">
                    <label for="id_first_name" class="form-label">
                      <i class="fas fa-user me-2 text-primary"></i>الاسم الأول
                    </label>
                    <input type="text" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}"
                           id="id_first_name" name="first_name"
                           value="{{ form.first_name.value|default:'' }}"
                           placeholder="أدخل اسمك الأول" required>
                    {% if form.first_name.errors %}
                      <div class="invalid-feedback">{{ form.first_name.errors.0 }}</div>
                    {% endif %}
                  </div>
                  <div class="col-md-6 mb-4">
                    <label for="id_last_name" class="form-label">
                      <i class="fas fa-user me-2 text-primary"></i>اسم العائلة
                    </label>
                    <input type="text" class="form-control {% if form.last_name.errors %}is-invalid{% endif %}"
                           id="id_last_name" name="last_name"
                           value="{{ form.last_name.value|default:'' }}"
                           placeholder="أدخل اسم العائلة" required>
                    {% if form.last_name.errors %}
                      <div class="invalid-feedback">{{ form.last_name.errors.0 }}</div>
                    {% endif %}
                  </div>
                </div>

                <div class="mb-4">
                  <label for="id_username" class="form-label">
                    <i class="fas fa-at me-2 text-primary"></i>اسم المستخدم
                  </label>
                  <input type="text" class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                         id="id_username" name="username"
                         value="{{ form.username.value|default:'' }}"
                         placeholder="اختر اسم مستخدم فريد" required>
                  {% if form.username.errors %}
                    <div class="invalid-feedback">{{ form.username.errors.0 }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="id_email" class="form-label">
                    <i class="fas fa-envelope me-2 text-primary"></i>البريد الإلكتروني
                  </label>
                  <input type="email" class="form-control {% if form.email.errors %}is-invalid{% endif %}"
                         id="id_email" name="email"
                         value="{{ form.email.value|default:'' }}"
                         placeholder="أدخل بريدك الإلكتروني" required>
                  {% if form.email.errors %}
                    <div class="invalid-feedback">{{ form.email.errors.0 }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="id_phone" class="form-label">
                    <i class="fas fa-phone me-2 text-primary"></i>رقم الهاتف (اختياري)
                  </label>
                  <input type="tel" class="form-control {% if form.phone.errors %}is-invalid{% endif %}"
                         id="id_phone" name="phone"
                         value="{{ form.phone.value|default:'' }}"
                         placeholder="أدخل رقم هاتفك">
                  {% if form.phone.errors %}
                    <div class="invalid-feedback">{{ form.phone.errors.0 }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="id_password1" class="form-label">
                    <i class="fas fa-lock me-2 text-primary"></i>كلمة المرور
                  </label>
                  <div class="input-group">
                    <input type="password" class="form-control {% if form.password1.errors %}is-invalid{% endif %}"
                           id="id_password1" name="password1"
                           placeholder="أنشئ كلمة مرور قوية" required>
                    <button class="btn btn-outline-secondary" type="button" id="toggleCustomerPassword">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div class="password-strength" id="customerPasswordStrength"></div>
                  <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    يجب أن تكون كلمة المرور 8 أحرف على الأقل
                  </small>
                  {% if form.password1.errors %}
                    <div class="invalid-feedback">{{ form.password1.errors.0 }}</div>
                  {% endif %}
                </div>

                <div class="mb-4">
                  <label for="id_password2" class="form-label">
                    <i class="fas fa-lock me-2 text-primary"></i>تأكيد كلمة المرور
                  </label>
                  <input type="password" class="form-control {% if form.password2.errors %}is-invalid{% endif %}"
                         id="id_password2" name="password2"
                         placeholder="أكد كلمة المرور" required>
                  {% if form.password2.errors %}
                    <div class="invalid-feedback">{{ form.password2.errors.0 }}</div>
                  {% endif %}
                  <div class="invalid-feedback" id="customerPasswordMismatch" style="display: none;">كلمات المرور غير متطابقة</div>
                </div>

                <div class="mb-4">
                  <div class="form-check d-flex align-items-start">
                    <input class="form-check-input mt-1 me-3" type="checkbox" id="customerTerms" required>
                    <label class="form-check-label text-start" for="customerTerms">
                      أوافق على <a href="/terms/" target="_blank" class="text-decoration-none fw-semibold">شروط الخدمة</a> و
                      <a href="/privacy/" target="_blank" class="text-decoration-none fw-semibold">سياسة الخصوصية</a>
                    </label>
                  </div>
                </div>

                <button type="submit" class="btn btn-register w-100 mb-4">
                  <i class="fas fa-user-plus me-2"></i>
                  <span class="fw-bold">إنشاء حساب عميل</span>
                </button>
              </form>
            </div>
            <div class="tab-pane fade" id="storeowner" role="tabpanel" aria-labelledby="storeowner-tab">
              <div class="text-center mb-5">
                <div class="role-icon" style="color: var(--primary-blue);">
                  <i class="fas fa-store"></i>
                </div>
                <h3 class="fw-bold mb-3" style="color: var(--primary-blue);">انضم كصاحب متجر</h3>
                <p class="text-muted">ابدأ في بيع منتجاتك وانمِ أعمالك معنا</p>
                <div class="row g-2 justify-content-center mt-3">
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-chart-line text-success me-1"></i>إحصائيات مفصلة
                    </span>
                  </div>
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-shipping-fast text-info me-1"></i>إدارة الشحن
                    </span>
                  </div>
                  <div class="col-auto">
                    <span class="badge bg-light text-dark border">
                      <i class="fas fa-credit-card text-warning me-1"></i>مدفوعات آمنة
                    </span>
                  </div>
                </div>
              </div>

              <form method="post" id="storeOwnerForm">
                {% csrf_token %}
                <input type="hidden" name="role" value="store_owner">

                <div class="row">
                  <div class="col-md-6 mb-4">
                    <label for="store_first_name" class="form-label">
                      <i class="fas fa-user me-2 text-primary"></i>الاسم الأول
                    </label>
                    <input type="text" class="form-control" id="store_first_name" name="first_name" value="{{ request.POST.first_name }}" placeholder="أدخل اسمك الأول" required>
                  </div>
                  <div class="col-md-6 mb-4">
                    <label for="store_last_name" class="form-label">
                      <i class="fas fa-user me-2 text-primary"></i>اسم العائلة
                    </label>
                    <input type="text" class="form-control" id="store_last_name" name="last_name" value="{{ request.POST.last_name }}" placeholder="أدخل اسم العائلة" required>
                  </div>
                </div>

                <div class="mb-4">
                  <label for="store_username" class="form-label">
                    <i class="fas fa-at me-2 text-primary"></i>اسم المستخدم
                  </label>
                  <input type="text" class="form-control {% if form.errors.username %}is-invalid{% endif %}" id="store_username" name="username" value="{{ request.POST.username }}" placeholder="اختر اسم مستخدم فريد" required>
                  {% if form.errors.username %}<div class="invalid-feedback">{{ form.errors.username.0 }}</div>{% endif %}
                </div>

                <div class="mb-4">
                  <label for="store_email" class="form-label">
                    <i class="fas fa-envelope me-2 text-primary"></i>البريد الإلكتروني التجاري
                  </label>
                  <input type="email" class="form-control {% if form.errors.email %}is-invalid{% endif %}" id="store_email" name="email" value="{{ request.POST.email }}" placeholder="أدخل بريدك الإلكتروني التجاري" required>
                  {% if form.errors.email %}<div class="invalid-feedback">{{ form.errors.email.0 }}</div>{% endif %}
                </div>

                <div class="mb-4">
                  <label for="store_password" class="form-label">
                    <i class="fas fa-lock me-2 text-primary"></i>كلمة المرور
                  </label>
                  <div class="input-group">
                    <input type="password" class="form-control {% if form.errors.password %}is-invalid{% endif %}" id="store_password" name="password" placeholder="أنشئ كلمة مرور قوية" required>
                    <button class="btn btn-outline-secondary" type="button" id="toggleStorePassword">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div class="password-strength" id="storePasswordStrength"></div>
                  <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    يجب أن تكون كلمة المرور 8 أحرف على الأقل
                  </small>
                  {% if form.errors.password %}<div class="invalid-feedback">{{ form.errors.password.0 }}</div>{% endif %}
                </div>

                <div class="mb-4">
                  <label for="store_password_confirm" class="form-label">
                    <i class="fas fa-lock me-2 text-primary"></i>تأكيد كلمة المرور
                  </label>
                  <input type="password" class="form-control" id="store_password_confirm" name="password_confirm" placeholder="أكد كلمة المرور" required>
                  <div class="invalid-feedback" id="storePasswordMismatch" style="display: none;">كلمات المرور غير متطابقة</div>
                </div>

                <div class="mb-4">
                  <label for="store_name" class="form-label">
                    <i class="fas fa-store me-2 text-primary"></i>اسم المتجر
                  </label>
                  <input type="text" class="form-control {% if form.errors.store_name %}is-invalid{% endif %}" id="store_name" name="store_name" value="{{ request.POST.store_name }}" placeholder="أدخل اسم متجرك" required>
                  {% if form.errors.store_name %}<div class="invalid-feedback">{{ form.errors.store_name.0 }}</div>{% endif %}
                </div>

                <div class="row">
                  <div class="col-md-6 mb-4">
                    <label for="business_license" class="form-label">
                      <i class="fas fa-certificate me-2 text-primary"></i>رخصة العمل
                    </label>
                    <input type="text" class="form-control {% if form.errors.business_license %}is-invalid{% endif %}" id="business_license" name="business_license" value="{{ request.POST.business_license }}" placeholder="أدخل رقم الرخصة" required>
                    {% if form.errors.business_license %}<div class="invalid-feedback">{{ form.errors.business_license.0 }}</div>{% endif %}
                  </div>
                  <div class="col-md-6 mb-4">
                    <label for="store_phone" class="form-label">
                      <i class="fas fa-phone me-2 text-primary"></i>رقم الهاتف
                    </label>
                    <input type="tel" class="form-control" id="store_phone" name="phone" value="{{ request.POST.phone }}" placeholder="أدخل رقم الهاتف" required>
                  </div>
                </div>

                <div class="mb-4">
                  <label for="store_address" class="form-label">
                    <i class="fas fa-map-marker-alt me-2 text-primary"></i>عنوان المتجر
                  </label>
                  <textarea class="form-control" id="store_address" name="address" rows="3" placeholder="أدخل عنوان متجرك بالتفصيل" required>{{ request.POST.address }}</textarea>
                </div>

                <div class="mb-4">
                  <div class="form-check d-flex align-items-start">
                    <input class="form-check-input mt-1 me-3" type="checkbox" id="storeTerms" required>
                    <label class="form-check-label text-start" for="storeTerms">
                      أوافق على <a href="/terms/" target="_blank" class="text-decoration-none fw-semibold">شروط الخدمة</a>،
                      <a href="/privacy/" target="_blank" class="text-decoration-none fw-semibold">سياسة الخصوصية</a>، و
                      <a href="/seller-agreement/" target="_blank" class="text-decoration-none fw-semibold">اتفاقية البائع</a>
                    </label>
                  </div>
                </div>

                <button type="submit" class="btn btn-register-store w-100 mb-4">
                  <i class="fas fa-store me-2"></i>
                  <span class="fw-bold">إنشاء حساب صاحب متجر</span>
                </button>
              </form>
            </div>
          </div>

          {% if success %}
            <div class="alert alert-success mt-4 text-center">
              <i class="fas fa-check-circle me-2"></i>
              <strong>تم التسجيل بنجاح!</strong> يمكنك الآن <a href="/login/" class="text-decoration-none fw-semibold">تسجيل الدخول</a>.
            </div>
          {% else %}
            <div class="mt-5 text-center">
              <div class="border-top pt-4">
                <p class="text-muted mb-2">هل لديك حساب بالفعل؟</p>
                <a href="/login/" class="btn btn-outline-primary btn-lg px-4">
                  <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </a>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تبديل عرض كلمة المرور
    const toggleButtons = document.querySelectorAll('[id^="toggle"]');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.id.replace('toggle', '').toLowerCase();
            let passwordInput;

            if (targetId === 'customer') {
                passwordInput = document.querySelector('#id_password1');
            } else if (targetId === 'store') {
                passwordInput = document.querySelector('#store_password');
            }

            const icon = this.querySelector('i');

            if (passwordInput && passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else if (passwordInput) {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // فحص قوة كلمة المرور
    const passwordInputs = document.querySelectorAll('#id_password1, #store_password');
    passwordInputs.forEach(input => {
        input.addEventListener('input', function() {
            let strengthBar;
            if (this.id === 'id_password1') {
                strengthBar = document.querySelector('#customerPasswordStrength');
            } else {
                strengthBar = document.querySelector('#storePasswordStrength');
            }

            const password = this.value;
            let strength = 0;

            if (password.length >= 8) strength += 25;
            if (/[a-z]/.test(password)) strength += 25;
            if (/[A-Z]/.test(password)) strength += 25;
            if (/[0-9]/.test(password)) strength += 25;

            if (strengthBar) {
                strengthBar.style.width = strength + '%';

                if (strength < 50) {
                    strengthBar.style.backgroundColor = '#dc3545';
                } else if (strength < 75) {
                    strengthBar.style.backgroundColor = '#ffc107';
                } else {
                    strengthBar.style.backgroundColor = '#28a745';
                }
            }
        });
    });

    // فحص تطابق كلمات المرور
    const confirmInputs = document.querySelectorAll('#id_password2, #store_password_confirm');
    confirmInputs.forEach(input => {
        input.addEventListener('input', function() {
            let originalPassword, mismatchDiv;

            if (this.id === 'id_password2') {
                originalPassword = document.querySelector('#id_password1');
                mismatchDiv = document.querySelector('#customerPasswordMismatch');
            } else {
                originalPassword = document.querySelector('#store_password');
                mismatchDiv = document.querySelector('#storePasswordMismatch');
            }

            if (originalPassword && mismatchDiv) {
                if (this.value !== originalPassword.value && this.value.length > 0) {
                    mismatchDiv.style.display = 'block';
                    this.classList.add('is-invalid');
                } else {
                    mismatchDiv.style.display = 'none';
                    this.classList.remove('is-invalid');
                }
            }
        });
    });

    // تأثيرات بصرية للتبويبات
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function() {
            const targetPane = document.querySelector(this.getAttribute('data-bs-target'));
            if (targetPane) {
                targetPane.style.opacity = '0';
                targetPane.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    targetPane.style.transition = 'all 0.4s ease';
                    targetPane.style.opacity = '1';
                    targetPane.style.transform = 'translateY(0)';
                }, 50);
            }
        });
    });

    // التأكد من إرسال النموذج الصحيح
    const customerForm = document.getElementById('customerForm');
    const storeOwnerForm = document.getElementById('storeOwnerForm');

    if (customerForm) {
        customerForm.addEventListener('submit', function(e) {
            console.log('Customer form submitted');
        });
    }

    if (storeOwnerForm) {
        storeOwnerForm.addEventListener('submit', function(e) {
            console.log('Store owner form submitted');
        });
    }
});
</script>

{% endblock %}
