{% extends 'core/base.html' %}
{% block title %}Register | Best in Click{% endblock %}
{% block content %}
<style>
  .register-container {
    min-height: 90vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2rem;
    margin: 1rem 0;
  }
  .register-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 1.5rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }
  .nav-tabs {
    border: none;
    background: #f8f9fa;
    border-radius: 1rem;
    padding: 0.25rem;
  }
  .nav-tabs .nav-link {
    border: none;
    border-radius: 0.75rem;
    color: #6c757d;
    font-weight: 600;
    transition: all 0.3s;
  }
  .nav-tabs .nav-link.active {
    background: #0d6efd;
    color: white;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
  }
  .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
  .btn-register {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem;
    font-weight: 600;
    transition: all 0.3s;
  }
  .btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
  }
  .btn-register-store {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem;
    font-weight: 600;
    transition: all 0.3s;
  }
  .btn-register-store:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
  }
  .password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.5rem;
    transition: all 0.3s;
  }
  .role-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
</style>

<div class="container-fluid register-container d-flex align-items-center justify-content-center">
  <div class="row w-100 justify-content-center">
    <div class="col-md-8 col-lg-6">
      <div class="card register-card">
        <div class="card-body p-5">
          <div class="text-center mb-4">
            <div class="mb-3">
              <i class="fas fa-user-plus fa-3x text-primary"></i>
            </div>
            <h2 class="fw-bold text-primary mb-2">Join Best in Click</h2>
            <p class="text-muted">Choose your role and create your account to get started</p>
          </div>

          {% if messages %}
            {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
              </div>
            {% endfor %}
          {% endif %}

          {% if form.errors %}
            <div class="alert alert-danger alert-dismissible fade show">
              <i class="fas fa-exclamation-triangle me-2"></i>
              {% for field in form %}
                {% for error in field.errors %}
                  <div>{{ error }}</div>
                {% endfor %}
              {% endfor %}
              {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
              {% endfor %}
              <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
          {% endif %}
          <ul class="nav nav-tabs mb-4" id="registerTabs" role="tablist">
            <li class="nav-item flex-fill" role="presentation">
              <button class="nav-link active w-100" id="customer-tab" data-bs-toggle="tab" data-bs-target="#customer" type="button" role="tab" aria-controls="customer" aria-selected="true">
                <i class="fas fa-shopping-cart me-2"></i>Customer
              </button>
            </li>
            <li class="nav-item flex-fill" role="presentation">
              <button class="nav-link w-100" id="storeowner-tab" data-bs-toggle="tab" data-bs-target="#storeowner" type="button" role="tab" aria-controls="storeowner" aria-selected="false">
                <i class="fas fa-store me-2"></i>Store Owner
              </button>
            </li>
          </ul>
          <div class="tab-content" id="registerTabsContent">
            <div class="tab-pane fade show active" id="customer" role="tabpanel" aria-labelledby="customer-tab">
              <div class="text-center mb-4">
                <div class="role-icon text-success">
                  <i class="fas fa-shopping-cart"></i>
                </div>
                <h5 class="text-success">Join as a Customer</h5>
                <p class="text-muted small">Discover amazing products and enjoy personalized shopping experience</p>
              </div>

              <form method="post" action="/register/" id="customerForm">
                {% csrf_token %}
                <input type="hidden" name="role" value="customer">

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="customer_first_name" class="form-label fw-semibold">
                      <i class="fas fa-user me-2"></i>First Name
                    </label>
                    <input type="text" class="form-control form-control-lg" id="customer_first_name" name="first_name" value="{{ request.POST.first_name }}" placeholder="Enter your first name" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="customer_last_name" class="form-label fw-semibold">
                      <i class="fas fa-user me-2"></i>Last Name
                    </label>
                    <input type="text" class="form-control form-control-lg" id="customer_last_name" name="last_name" value="{{ request.POST.last_name }}" placeholder="Enter your last name" required>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="customer_username" class="form-label fw-semibold">
                    <i class="fas fa-at me-2"></i>Username
                  </label>
                  <input type="text" class="form-control form-control-lg {% if form.errors.username %}is-invalid{% endif %}" id="customer_username" name="username" value="{{ request.POST.username }}" placeholder="Choose a unique username" required>
                  {% if form.errors.username %}<div class="invalid-feedback">{{ form.errors.username.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="customer_email" class="form-label fw-semibold">
                    <i class="fas fa-envelope me-2"></i>Email Address
                  </label>
                  <input type="email" class="form-control form-control-lg {% if form.errors.email %}is-invalid{% endif %}" id="customer_email" name="email" value="{{ request.POST.email }}" placeholder="Enter your email address" required>
                  {% if form.errors.email %}<div class="invalid-feedback">{{ form.errors.email.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="customer_password" class="form-label fw-semibold">
                    <i class="fas fa-lock me-2"></i>Password
                  </label>
                  <div class="input-group">
                    <input type="password" class="form-control form-control-lg {% if form.errors.password %}is-invalid{% endif %}" id="customer_password" name="password" placeholder="Create a strong password" required>
                    <button class="btn btn-outline-secondary" type="button" id="toggleCustomerPassword">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div class="password-strength bg-light" id="customerPasswordStrength"></div>
                  <small class="text-muted">Password must be at least 8 characters long</small>
                  {% if form.errors.password %}<div class="invalid-feedback">{{ form.errors.password.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="customer_password_confirm" class="form-label fw-semibold">
                    <i class="fas fa-lock me-2"></i>Confirm Password
                  </label>
                  <input type="password" class="form-control form-control-lg" id="customer_password_confirm" name="password_confirm" placeholder="Confirm your password" required>
                  <div class="invalid-feedback" id="customerPasswordMismatch" style="display: none;">Passwords do not match</div>
                </div>

                <div class="mb-3 form-check">
                  <input class="form-check-input" type="checkbox" id="customerTerms" required>
                  <label class="form-check-label" for="customerTerms">
                    I agree to the <a href="/terms/" target="_blank">Terms of Service</a> and <a href="/privacy/" target="_blank">Privacy Policy</a>
                  </label>
                </div>

                <button type="submit" class="btn btn-register text-white w-100 mb-3">
                  <i class="fas fa-user-plus me-2"></i>Create Customer Account
                </button>
              </form>
            </div>
            <div class="tab-pane fade" id="storeowner" role="tabpanel" aria-labelledby="storeowner-tab">
              <div class="text-center mb-4">
                <div class="role-icon text-primary">
                  <i class="fas fa-store"></i>
                </div>
                <h5 class="text-primary">Join as a Store Owner</h5>
                <p class="text-muted small">Start selling your products and grow your business with us</p>
              </div>

              <form method="post" action="/register/" id="storeOwnerForm">
                {% csrf_token %}
                <input type="hidden" name="role" value="store_owner">

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="store_first_name" class="form-label fw-semibold">
                      <i class="fas fa-user me-2"></i>First Name
                    </label>
                    <input type="text" class="form-control form-control-lg" id="store_first_name" name="first_name" value="{{ request.POST.first_name }}" placeholder="Enter your first name" required>
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="store_last_name" class="form-label fw-semibold">
                      <i class="fas fa-user me-2"></i>Last Name
                    </label>
                    <input type="text" class="form-control form-control-lg" id="store_last_name" name="last_name" value="{{ request.POST.last_name }}" placeholder="Enter your last name" required>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="store_username" class="form-label fw-semibold">
                    <i class="fas fa-at me-2"></i>Username
                  </label>
                  <input type="text" class="form-control form-control-lg {% if form.errors.username %}is-invalid{% endif %}" id="store_username" name="username" value="{{ request.POST.username }}" placeholder="Choose a unique username" required>
                  {% if form.errors.username %}<div class="invalid-feedback">{{ form.errors.username.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="store_email" class="form-label fw-semibold">
                    <i class="fas fa-envelope me-2"></i>Email Address
                  </label>
                  <input type="email" class="form-control form-control-lg {% if form.errors.email %}is-invalid{% endif %}" id="store_email" name="email" value="{{ request.POST.email }}" placeholder="Enter your business email" required>
                  {% if form.errors.email %}<div class="invalid-feedback">{{ form.errors.email.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="store_password" class="form-label fw-semibold">
                    <i class="fas fa-lock me-2"></i>Password
                  </label>
                  <div class="input-group">
                    <input type="password" class="form-control form-control-lg {% if form.errors.password %}is-invalid{% endif %}" id="store_password" name="password" placeholder="Create a strong password" required>
                    <button class="btn btn-outline-secondary" type="button" id="toggleStorePassword">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                  <div class="password-strength bg-light" id="storePasswordStrength"></div>
                  {% if form.errors.password %}<div class="invalid-feedback">{{ form.errors.password.0 }}</div>{% endif %}
                </div>

                <div class="mb-3">
                  <label for="store_password_confirm" class="form-label fw-semibold">
                    <i class="fas fa-lock me-2"></i>Confirm Password
                  </label>
                  <input type="password" class="form-control form-control-lg" id="store_password_confirm" name="password_confirm" placeholder="Confirm your password" required>
                  <div class="invalid-feedback" id="storePasswordMismatch" style="display: none;">Passwords do not match</div>
                </div>

                <div class="mb-3">
                  <label for="store_name" class="form-label fw-semibold">
                    <i class="fas fa-store me-2"></i>Store Name
                  </label>
                  <input type="text" class="form-control form-control-lg {% if form.errors.store_name %}is-invalid{% endif %}" id="store_name" name="store_name" value="{{ request.POST.store_name }}" placeholder="Enter your store name" required>
                  {% if form.errors.store_name %}<div class="invalid-feedback">{{ form.errors.store_name.0 }}</div>{% endif %}
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                    <label for="business_license" class="form-label fw-semibold">
                      <i class="fas fa-certificate me-2"></i>Business License
                    </label>
                    <input type="text" class="form-control form-control-lg {% if form.errors.business_license %}is-invalid{% endif %}" id="business_license" name="business_license" value="{{ request.POST.business_license }}" placeholder="Enter license number" required>
                    {% if form.errors.business_license %}<div class="invalid-feedback">{{ form.errors.business_license.0 }}</div>{% endif %}
                  </div>
                  <div class="col-md-6 mb-3">
                    <label for="store_phone" class="form-label fw-semibold">
                      <i class="fas fa-phone me-2"></i>Phone Number
                    </label>
                    <input type="tel" class="form-control form-control-lg" id="store_phone" name="phone" value="{{ request.POST.phone }}" placeholder="Enter phone number" required>
                  </div>
                </div>

                <div class="mb-3">
                  <label for="store_address" class="form-label fw-semibold">
                    <i class="fas fa-map-marker-alt me-2"></i>Store Address
                  </label>
                  <textarea class="form-control" id="store_address" name="address" rows="2" placeholder="Enter your store address" required>{{ request.POST.address }}</textarea>
                </div>

                <div class="mb-3 form-check">
                  <input class="form-check-input" type="checkbox" id="storeTerms" required>
                  <label class="form-check-label" for="storeTerms">
                    I agree to the <a href="/terms/" target="_blank">Terms of Service</a>, <a href="/privacy/" target="_blank">Privacy Policy</a>, and <a href="/seller-agreement/" target="_blank">Seller Agreement</a>
                  </label>
                </div>

                <button type="submit" class="btn btn-register-store text-white w-100 mb-3">
                  <i class="fas fa-store me-2"></i>Create Store Owner Account
                </button>
              </form>
            </div>
          </div>
        {% if success %}
          <div class="alert alert-success mt-4">Registration successful! You can now <a href="/login/">login</a>.</div>
        {% else %}
        <div class="mt-4 text-center">
          <span class="text-muted">Already have an account?</span>
          <a href="/login/" class="ms-1">Login here</a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
